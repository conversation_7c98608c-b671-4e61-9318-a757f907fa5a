import typeorm = require('typeorm');

class AwqDeX1750655065452 implements typeorm.MigrationInterface {
    name = 'AwqDeX1750655065452';

    public async up(queryRunner: typeorm.QueryRunner): Promise<void> {
        await queryRunner.query(`
        ALTER TABLE \`user\` CHANGE \`phone\` \`phone\` varchar(64) NULL COMMENT '用户手机号'
    `);
    }

    public async down(queryRunner: typeorm.QueryRunner): Promise<void> {
        await queryRunner.query(`
        ALTER TABLE \`user\` CHANGE \`phone\` \`phone\` varchar(64) NOT NULL COMMENT '用户手机号'
    `);
    }
}

module.exports = AwqDeX1750655065452;
