import typeorm = require('typeorm');

class NhpKsJ1750653881240 implements typeorm.MigrationInterface {
    name = 'NhpKsJ1750653881240';

    public async up(queryRunner: typeorm.QueryRunner): Promise<void> {
        await queryRunner.query(`
        CREATE TABLE \`content_comment\` (\`id\` varchar(36) NOT NULL, \`body\` text NOT NULL COMMENT '评论内容', \`createdAt\` datetime(6) NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP(6), \`mpath\` varchar(255) NULL DEFAULT '', \`postId\` varchar(36) NOT NULL, \`parentId\` varchar(36) NULL, \`authorId\` varchar(36) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB
    `);
        await queryRunner.query(`
        CREATE TABLE \`content_tag\` (\`id\` varchar(36) NOT NULL, \`name\` varchar(255) NOT NULL COMMENT '标签名称', \`desc\` varchar(255) NULL COMMENT '标签描述', UNIQUE INDEX \`IDX_80a6b11237714419146b2e18e6\` (\`name\`), PRIMARY KEY (\`id\`)) ENGINE=InnoDB
    `);
        await queryRunner.query(`
        CREATE TABLE \`content_posts\` (\`id\` varchar(36) NOT NULL, \`title\` varchar(255) NOT NULL COMMENT '文章标题', \`body\` text NOT NULL COMMENT '文章内容', \`summary\` varchar(255) NULL COMMENT '文章描述', \`keywords\` text NULL COMMENT '关键字', \`type\` enum ('html', 'markdown') NOT NULL COMMENT '文章类型' DEFAULT 'html', \`publishedAt\` timestamp NULL COMMENT '发布时间', \`customOrder\` int NOT NULL COMMENT '自定义文章排序' DEFAULT '0', \`createdAt\` datetime(6) NOT NULL COMMENT '创建时间' DEFAULT CURRENT_TIMESTAMP(6), \`updatedAt\` datetime(6) NULL COMMENT '更新时间' DEFAULT CURRENT_TIMESTAMP(6) ON UPDATE CURRENT_TIMESTAMP(6), \`deleteAt\` datetime(6) NULL COMMENT '删除时间', \`categoryId\` varchar(36) NULL, \`authorId\` varchar(36) NOT NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB
    `);
        await queryRunner.query(`
        CREATE TABLE \`content_category\` (\`id\` varchar(36) NOT NULL, \`name\` varchar(255) NOT NULL COMMENT '分类名称', \`customOrder\` int NOT NULL COMMENT '分类排序' DEFAULT '0', \`mpath\` varchar(255) NULL DEFAULT '', \`parentId\` varchar(36) NULL, PRIMARY KEY (\`id\`)) ENGINE=InnoDB
    `);
        await queryRunner.query(`
        CREATE TABLE \`content_posts_tags_content_tag\` (\`contentPostsId\` varchar(36) NOT NULL, \`contentTagId\` varchar(36) NOT NULL, INDEX \`IDX_5403616c3d5f8d4819088a3aec\` (\`contentPostsId\`), INDEX \`IDX_7b03a86b1bfc2fdcb189841f31\` (\`contentTagId\`), PRIMARY KEY (\`contentPostsId\`, \`contentTagId\`)) ENGINE=InnoDB
    `);
        await queryRunner.query(`
        ALTER TABLE \`content_comment\` ADD CONSTRAINT \`FK_937464642ad2fe050807b731fa9\` FOREIGN KEY (\`postId\`) REFERENCES \`content_posts\`(\`id\`) ON DELETE CASCADE ON UPDATE CASCADE
    `);
        await queryRunner.query(`
        ALTER TABLE \`content_comment\` ADD CONSTRAINT \`FK_cf594b7930bbee3ef9cb94cc083\` FOREIGN KEY (\`parentId\`) REFERENCES \`content_comment\`(\`id\`) ON DELETE CASCADE ON UPDATE NO ACTION
    `);
        await queryRunner.query(`
        ALTER TABLE \`content_comment\` ADD CONSTRAINT \`FK_70b9cb9c33e723c66ea68715268\` FOREIGN KEY (\`authorId\`) REFERENCES \`user\`(\`id\`) ON DELETE CASCADE ON UPDATE CASCADE
    `);
        await queryRunner.query(`
        ALTER TABLE \`content_posts\` ADD CONSTRAINT \`FK_4027367881933f659d02f367e92\` FOREIGN KEY (\`categoryId\`) REFERENCES \`content_category\`(\`id\`) ON DELETE SET NULL ON UPDATE NO ACTION
    `);
        await queryRunner.query(`
        ALTER TABLE \`content_posts\` ADD CONSTRAINT \`FK_8fcc2d81ced7b8ade2bbd151b1a\` FOREIGN KEY (\`authorId\`) REFERENCES \`user\`(\`id\`) ON DELETE CASCADE ON UPDATE CASCADE
    `);
        await queryRunner.query(`
        ALTER TABLE \`content_category\` ADD CONSTRAINT \`FK_6b5841be7e225a803a2f251b0fc\` FOREIGN KEY (\`parentId\`) REFERENCES \`content_category\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION
    `);
        await queryRunner.query(`
        ALTER TABLE \`user_refresh_token\` ADD CONSTRAINT \`FK_0fb9e76570bb35fd7dd7f78f73c\` FOREIGN KEY (\`accessTokenId\`) REFERENCES \`user_access_token\`(\`id\`) ON DELETE CASCADE ON UPDATE NO ACTION
    `);
        await queryRunner.query(`
        ALTER TABLE \`user_access_token\` ADD CONSTRAINT \`FK_c9c6ac4970ddbe5a8c4887e1e7e\` FOREIGN KEY (\`userId\`) REFERENCES \`user\`(\`id\`) ON DELETE CASCADE ON UPDATE NO ACTION
    `);
        await queryRunner.query(`
        ALTER TABLE \`content_posts_tags_content_tag\` ADD CONSTRAINT \`FK_5403616c3d5f8d4819088a3aec3\` FOREIGN KEY (\`contentPostsId\`) REFERENCES \`content_posts\`(\`id\`) ON DELETE CASCADE ON UPDATE CASCADE
    `);
        await queryRunner.query(`
        ALTER TABLE \`content_posts_tags_content_tag\` ADD CONSTRAINT \`FK_7b03a86b1bfc2fdcb189841f314\` FOREIGN KEY (\`contentTagId\`) REFERENCES \`content_tag\`(\`id\`) ON DELETE NO ACTION ON UPDATE NO ACTION
    `);
    }

    public async down(queryRunner: typeorm.QueryRunner): Promise<void> {
        await queryRunner.query(`
        ALTER TABLE \`content_posts_tags_content_tag\` DROP FOREIGN KEY \`FK_7b03a86b1bfc2fdcb189841f314\`
    `);
        await queryRunner.query(`
        ALTER TABLE \`content_posts_tags_content_tag\` DROP FOREIGN KEY \`FK_5403616c3d5f8d4819088a3aec3\`
    `);
        await queryRunner.query(`
        ALTER TABLE \`user_access_token\` DROP FOREIGN KEY \`FK_c9c6ac4970ddbe5a8c4887e1e7e\`
    `);
        await queryRunner.query(`
        ALTER TABLE \`user_refresh_token\` DROP FOREIGN KEY \`FK_0fb9e76570bb35fd7dd7f78f73c\`
    `);
        await queryRunner.query(`
        ALTER TABLE \`content_category\` DROP FOREIGN KEY \`FK_6b5841be7e225a803a2f251b0fc\`
    `);
        await queryRunner.query(`
        ALTER TABLE \`content_posts\` DROP FOREIGN KEY \`FK_8fcc2d81ced7b8ade2bbd151b1a\`
    `);
        await queryRunner.query(`
        ALTER TABLE \`content_posts\` DROP FOREIGN KEY \`FK_4027367881933f659d02f367e92\`
    `);
        await queryRunner.query(`
        ALTER TABLE \`content_comment\` DROP FOREIGN KEY \`FK_70b9cb9c33e723c66ea68715268\`
    `);
        await queryRunner.query(`
        ALTER TABLE \`content_comment\` DROP FOREIGN KEY \`FK_cf594b7930bbee3ef9cb94cc083\`
    `);
        await queryRunner.query(`
        ALTER TABLE \`content_comment\` DROP FOREIGN KEY \`FK_937464642ad2fe050807b731fa9\`
    `);
        await queryRunner.query(`
        DROP INDEX \`IDX_7b03a86b1bfc2fdcb189841f31\` ON \`content_posts_tags_content_tag\`
    `);
        await queryRunner.query(`
        DROP INDEX \`IDX_5403616c3d5f8d4819088a3aec\` ON \`content_posts_tags_content_tag\`
    `);
        await queryRunner.query(`
        DROP TABLE \`content_posts_tags_content_tag\`
    `);
        await queryRunner.query(`
        DROP TABLE \`content_category\`
    `);
        await queryRunner.query(`
        DROP TABLE \`content_posts\`
    `);
        await queryRunner.query(`
        DROP INDEX \`IDX_80a6b11237714419146b2e18e6\` ON \`content_tag\`
    `);
        await queryRunner.query(`
        DROP TABLE \`content_tag\`
    `);
        await queryRunner.query(`
        DROP TABLE \`content_comment\`
    `);
    }
}

module.exports = NhpKsJ1750653881240;
